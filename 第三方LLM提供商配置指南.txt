
🔧 AI助手软件 - 第三方LLM提供商配置指南

📋 支持的提供商:
   • OpenAI (https://api.openai.com)
   • Claude/Anthropic (https://api.anthropic.com)
   • aihubmix (https://aihubmix.com)
   • DeepSeek (https://api.deepseek.com)
   • Moonshot (https://api.moonshot.cn)
   • 智谱AI (https://open.bigmodel.cn)
   • 豆包 (https://ark.cn-beijing.volces.com)
   • 自定义提供商

🚀 配置步骤:
   1. 启动AI助手软件
   2. 点击"设置"菜单
   3. 选择"模型"配置页面
   4. 点击"添加"按钮
   5. 填写提供商信息:
      - 名称: 自定义名称
      - 提供商: 选择对应的提供商类型
      - API地址: 提供商的API地址
      - API密钥: 您的API密钥
      - 模型: 选择或输入模型名称
      - 最大Token数: 设置合适的值

🔑 获取API密钥:
   • OpenAI: https://platform.openai.com/api-keys
   • Claude: https://console.anthropic.com/
   • aihubmix: https://aihubmix.com/
   • DeepSeek: https://platform.deepseek.com/
   • Moonshot: https://platform.moonshot.cn/
   • 智谱AI: https://open.bigmodel.cn/
   • 豆包: https://console.volcengine.com/

⚙️ 配置示例:

OpenAI配置:
   - 名称: OpenAI
   - API地址: https://api.openai.com
   - API密钥: sk-xxxxxxxxxxxxxxxx
   - 模型: gpt-4 或 gpt-3.5-turbo

Claude配置:
   - 名称: Claude
   - API地址: https://api.anthropic.com
   - API密钥: sk-ant-xxxxxxxxxxxxxxxx
   - 模型: claude-3-sonnet-20240229

aihubmix配置:
   - 名称: aihubmix
   - API地址: https://api.aihubmix.com/v1
   - API密钥: 您的aihubmix密钥
   - 模型: gpt-4 或其他支持的模型

🛠️ 故障排除:
   • 如果提供商列表为空，请重启软件
   • 如果添加按钮无响应，检查软件日志
   • 如果API调用失败，检查网络连接和API密钥
   • 确保API地址格式正确（包含https://）

💡 使用建议:
   • 建议配置多个提供商作为备选
   • 根据任务类型选择合适的模型
   • 定期检查API密钥的有效性
   • 合理设置最大Token数以控制成本

📞 技术支持:
   • 本离线版本为独立修改版本
   • 如有问题请检查配置或重新安装
   • 确保第三方提供商的API服务正常
    